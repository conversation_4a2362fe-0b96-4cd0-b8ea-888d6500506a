<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Student;
use App\Models\ClassRoom;
use App\Models\Schedule;
use App\Models\User;
use App\Models\TimeSlot;
use Carbon\Carbon;

class SampleDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Tạo lớp học mẫu
        $teacher = User::where('role', 'teacher')->first();

        $class1 = ClassRoom::create([
            'name' => 'Lớp Toán 10A',
            'description' => 'Lớp học Toán lớp 10 ban A',
            'teacher_id' => $teacher->id,
            'max_students' => 30,
            'status' => 'active',
        ]);

        $class2 = ClassRoom::create([
            'name' => 'Lớp Văn 11B',
            'description' => 'Lớp học Văn lớp 11 ban B',
            'teacher_id' => $teacher->id,
            'max_students' => 25,
            'status' => 'active',
        ]);

        // Tạo học sinh mẫu
        $students = [
            ['name' => '<PERSON><PERSON>ễ<PERSON>', 'parent_name' => '<PERSON><PERSON><PERSON><PERSON>n <PERSON>', 'parent_phone' => '0901234567'],
            ['name' => 'Trần Thị Bảo', 'parent_name' => 'Trần Văn Cường', 'parent_phone' => '0901234568'],
            ['name' => 'Lê Minh Châu', 'parent_name' => 'Lê Văn Dũng', 'parent_phone' => '0901234569'],
            ['name' => 'Phạm Thị Diệu', 'parent_name' => 'Phạm Văn Em', 'parent_phone' => '0901234570'],
            ['name' => 'Hoàng Văn Phúc', 'parent_name' => 'Hoàng Văn Giang', 'parent_phone' => '0901234571'],
        ];

        foreach ($students as $index => $studentData) {
            $student = Student::create([
                'student_code' => Student::generateStudentCode(),
                'name' => $studentData['name'],
                'birth_date' => Carbon::now()->subYears(16)->subDays(rand(0, 365)),
                'parent_name' => $studentData['parent_name'],
                'parent_phone' => $studentData['parent_phone'],
                'google_drive_link' => 'https://drive.google.com/sample-' . ($index + 1),
                'status' => 'active',
            ]);

            // Thêm học sinh vào lớp
            $classToAssign = $index < 3 ? $class1 : $class2;
            $student->classes()->attach($classToAssign->id, [
                'enrolled_at' => Carbon::now()->subDays(30),
                'status' => 'active',
            ]);
        }

        // Tạo lịch dạy mẫu
        $timeSlots = TimeSlot::where('status', 'active')->take(3)->get();

        foreach ($timeSlots as $timeSlot) {
            Schedule::create([
                'class_id' => $class1->id,
                'teacher_id' => $teacher->id,
                'time_slot_id' => $timeSlot->id,
                'date' => Carbon::today(),
                'notes' => 'Lịch dạy mẫu cho ' . $timeSlot->name,
                'status' => 'scheduled',
            ]);
        }
    }
}
