<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class AdminUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Tạo tài khoản Admin
        User::create([
            'name' => 'Administrator',
            'email' => '<EMAIL>',
            'password' => Hash::make('admin123'),
            'role' => 'admin',
            'phone' => '0123456789',
            'address' => 'Trường học ABC',
            'birth_date' => '1980-01-01',
            'status' => 'active',
            'email_verified_at' => now(),
        ]);

        // Tạo tài khoản Quản sinh
        User::create([
            'name' => 'Quản sinh',
            'email' => '<EMAIL>',
            'password' => Hash::make('manager123'),
            'role' => 'manager',
            'phone' => '0123456788',
            'address' => 'Trường học ABC',
            'birth_date' => '1985-01-01',
            'status' => 'active',
            'email_verified_at' => now(),
        ]);

        // Tạo tài khoản Giáo viên
        User::create([
            'name' => 'Giáo viên',
            'email' => '<EMAIL>',
            'password' => Hash::make('teacher123'),
            'role' => 'teacher',
            'phone' => '0123456787',
            'address' => 'Trường học ABC',
            'birth_date' => '1990-01-01',
            'status' => 'active',
            'email_verified_at' => now(),
        ]);
    }
}
