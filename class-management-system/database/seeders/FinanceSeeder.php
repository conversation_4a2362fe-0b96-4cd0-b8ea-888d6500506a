<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Finance;
use App\Models\User;
use Carbon\Carbon;

class FinanceSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $admin = User::where('role', 'admin')->first();

        $finances = [
            // Thu nhập
            [
                'type' => 'income',
                'category' => 'Học phí',
                'description' => 'Thu học phí tháng 6/2025',
                'amount' => 15000000,
                'transaction_date' => Carbon::now()->subDays(5),
            ],
            [
                'type' => 'income',
                'category' => 'Học phí',
                'description' => 'Thu học phí tháng 5/2025',
                'amount' => 12000000,
                'transaction_date' => Carbon::now()->subDays(35),
            ],

            // Chi phí
            [
                'type' => 'expense',
                'category' => 'Lương',
                'description' => 'Lương giáo viên tháng 6/2025',
                'amount' => 8000000,
                'transaction_date' => Carbon::now()->subDays(3),
            ],
            [
                'type' => 'expense',
                'category' => 'Vật tư',
                'description' => 'Mua sách vở, dụng cụ học tập',
                'amount' => 2500000,
                'transaction_date' => Carbon::now()->subDays(7),
            ],
            [
                'type' => 'expense',
                'category' => 'Điện nước',
                'description' => 'Tiền điện nước tháng 6/2025',
                'amount' => 1200000,
                'transaction_date' => Carbon::now()->subDays(2),
            ],
        ];

        foreach ($finances as $finance) {
            Finance::create([
                'type' => $finance['type'],
                'category' => $finance['category'],
                'description' => $finance['description'],
                'amount' => $finance['amount'],
                'transaction_date' => $finance['transaction_date'],
                'recorded_by' => $admin->id,
                'reference_number' => 'REF' . rand(100000, 999999),
                'status' => 'completed',
            ]);
        }
    }
}
