<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\TimeSlot;

class TimeSlotSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $timeSlots = [
            // Khung giờ sáng
            ['name' => 'Sáng 1', 'period' => 'morning', 'start_time' => '07:00', 'end_time' => '08:30'],
            ['name' => 'Sáng 2', 'period' => 'morning', 'start_time' => '08:45', 'end_time' => '10:15'],
            ['name' => 'Sáng 3', 'period' => 'morning', 'start_time' => '10:30', 'end_time' => '12:00'],

            // Khung giờ chiều
            ['name' => 'Chiều 1', 'period' => 'afternoon', 'start_time' => '13:30', 'end_time' => '15:00'],
            ['name' => 'Chiều 2', 'period' => 'afternoon', 'start_time' => '15:15', 'end_time' => '16:45'],
            ['name' => 'Chiều 3', 'period' => 'afternoon', 'start_time' => '17:00', 'end_time' => '18:30'],

            // Khung giờ tối
            ['name' => 'Tối 1', 'period' => 'evening', 'start_time' => '19:00', 'end_time' => '20:30'],
            ['name' => 'Tối 2', 'period' => 'evening', 'start_time' => '20:45', 'end_time' => '22:15'],
        ];

        foreach ($timeSlots as $slot) {
            TimeSlot::create($slot);
        }
    }
}
