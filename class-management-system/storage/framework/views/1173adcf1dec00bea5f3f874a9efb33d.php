<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            Điểm danh - <?php echo e($schedule->class->name); ?>

        </h2>
     <?php $__env->endSlot(); ?>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Thông tin lịch dạy -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Thông tin lịch dạy</h3>
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div>
                            <span class="text-sm font-medium text-gray-500">Lớp:</span>
                            <p class="text-sm text-gray-900"><?php echo e($schedule->class->name); ?></p>
                        </div>
                        <div>
                            <span class="text-sm font-medium text-gray-500">Ngày:</span>
                            <p class="text-sm text-gray-900"><?php echo e($schedule->date->format('d/m/Y')); ?></p>
                        </div>
                        <div>
                            <span class="text-sm font-medium text-gray-500">Khung giờ:</span>
                            <p class="text-sm text-gray-900"><?php echo e($schedule->timeSlot->name); ?></p>
                        </div>
                        <div>
                            <span class="text-sm font-medium text-gray-500">Thời gian:</span>
                            <p class="text-sm text-gray-900"><?php echo e($schedule->timeSlot->start_time); ?> - <?php echo e($schedule->timeSlot->end_time); ?></p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Form điểm danh -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Điểm danh học sinh</h3>
                    
                    <form method="POST" action="<?php echo e(route('attendance.store-check-in', $schedule)); ?>">
                        <?php echo csrf_field(); ?>
                        
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mã HS</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tên học sinh</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Trạng thái</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ghi chú</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <?php $__currentLoopData = $schedule->class->activeStudents; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $student): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <?php
                                        $existingAttendance = $schedule->attendance->where('student_id', $student->id)->first();
                                    ?>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                            <?php echo e($student->student_code); ?>

                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            <?php echo e($student->name); ?>

                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <input type="hidden" name="attendances[<?php echo e($index); ?>][student_id]" value="<?php echo e($student->id); ?>">
                                            <select name="attendances[<?php echo e($index); ?>][status]" required
                                                    class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                                <option value="present" <?php echo e($existingAttendance && $existingAttendance->status === 'present' ? 'selected' : ''); ?>>Có mặt</option>
                                                <option value="absent" <?php echo e($existingAttendance && $existingAttendance->status === 'absent' ? 'selected' : ''); ?>>Vắng</option>
                                                <option value="late" <?php echo e($existingAttendance && $existingAttendance->status === 'late' ? 'selected' : ''); ?>>Muộn</option>
                                            </select>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <input type="text" name="attendances[<?php echo e($index); ?>][notes]" 
                                                   value="<?php echo e($existingAttendance ? $existingAttendance->notes : ''); ?>"
                                                   placeholder="Ghi chú..."
                                                   class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                        </td>
                                    </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>

                        <div class="mt-6 flex justify-between">
                            <a href="<?php echo e(route('schedules.my')); ?>" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                                Quay lại
                            </a>
                            <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                Lưu điểm danh
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH /Volumes/MOXC/Vscode/class-management-system/resources/views/attendance/check-in.blade.php ENDPATH**/ ?>