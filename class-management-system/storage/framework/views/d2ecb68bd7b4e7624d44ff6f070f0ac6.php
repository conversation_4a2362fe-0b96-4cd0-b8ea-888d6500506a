<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['active']));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['active']), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<?php
$classes = ($active ?? false)
            ? 'nav-hover inline-flex items-center px-4 pt-1 border-b-4 border-orange-400 text-lg font-semibold leading-5 text-orange-600 focus:outline-none focus:border-orange-700 transition duration-150 ease-in-out'
            : 'nav-hover inline-flex items-center px-4 pt-1 border-b-4 border-transparent text-lg font-medium leading-5 text-gray-600 hover:text-orange-600 hover:border-orange-300 focus:outline-none focus:text-orange-600 focus:border-orange-300 transition duration-150 ease-in-out';
?>

<a <?php echo e($attributes->merge(['class' => $classes])); ?>>
    <?php echo e($slot); ?>

</a>
<?php /**PATH /Volumes/MOXC/Vscode/class-management-system/resources/views/components/nav-link.blade.php ENDPATH**/ ?>