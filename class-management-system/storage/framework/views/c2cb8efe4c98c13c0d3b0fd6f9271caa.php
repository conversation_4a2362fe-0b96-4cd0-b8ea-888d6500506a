<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            Trang chủ - <?php echo e($user->name); ?> (<?php echo e(ucfirst($user->role)); ?>)
        </h2>
     <?php $__env->endSlot(); ?>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Thống kê tổng quan -->
            <?php if($user->isAdmin() || $user->isManager()): ?>
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                                </svg>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-600">Tổng học sinh</p>
                                <p class="text-2xl font-semibold text-gray-900"><?php echo e($totalStudents); ?></p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-green-100 text-green-600">
                                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                </svg>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-600">Tổng lớp học</p>
                                <p class="text-2xl font-semibold text-gray-900"><?php echo e($totalClasses); ?></p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-purple-100 text-purple-600">
                                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                </svg>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-600">Tổng giáo viên</p>
                                <p class="text-2xl font-semibold text-gray-900"><?php echo e($totalTeachers); ?></p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-red-100 text-red-600">
                                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                </svg>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-600">Vắng tháng này</p>
                                <p class="text-2xl font-semibold text-gray-900"><?php echo e($monthlyAbsent); ?></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <!-- Lịch dạy hôm nay -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">
                        <?php if($user->isTeacher()): ?>
                            Lịch dạy của tôi hôm nay
                        <?php else: ?>
                            Lịch dạy hôm nay (<?php echo e($today->format('d/m/Y')); ?>)
                        <?php endif; ?>
                    </h3>

                    <?php if($todaySchedules->count() > 0): ?>
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Khung giờ</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Lớp</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Giáo viên</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Thời gian</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Hành động</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <?php $__currentLoopData = $todaySchedules; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $schedule): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                            <?php echo e($schedule->timeSlot->name); ?>

                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            <?php echo e($schedule->class->name); ?>

                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            <?php echo e($schedule->teacher->name); ?>

                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            <?php echo e($schedule->timeSlot->start_time); ?> - <?php echo e($schedule->timeSlot->end_time); ?>

                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <?php if($user->isTeacher() && $schedule->teacher_id == $user->id): ?>
                                                <a href="<?php echo e(route('attendance.check-in', $schedule)); ?>" class="text-indigo-600 hover:text-indigo-900">Điểm danh</a>
                                            <?php elseif($user->isAdmin() || $user->isManager()): ?>
                                                <a href="<?php echo e(route('attendance.show', $schedule)); ?>" class="text-indigo-600 hover:text-indigo-900">Xem điểm danh</a>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <p class="text-gray-500">Không có lịch dạy nào hôm nay.</p>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Menu chức năng -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <?php if($user->isAdmin() || $user->isManager()): ?>
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Quản lý học sinh</h3>
                        <div class="space-y-2">
                            <a href="<?php echo e(route('students.index')); ?>" class="block text-indigo-600 hover:text-indigo-900">Danh sách học sinh</a>
                            <a href="<?php echo e(route('students.create')); ?>" class="block text-indigo-600 hover:text-indigo-900">Thêm học sinh mới</a>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Quản lý lớp học</h3>
                        <div class="space-y-2">
                            <a href="<?php echo e(route('classes.index')); ?>" class="block text-indigo-600 hover:text-indigo-900">Danh sách lớp học</a>
                            <a href="<?php echo e(route('classes.create')); ?>" class="block text-indigo-600 hover:text-indigo-900">Tạo lớp mới</a>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Lịch dạy</h3>
                        <div class="space-y-2">
                            <a href="<?php echo e(route('schedules.index')); ?>" class="block text-indigo-600 hover:text-indigo-900">Xem lịch dạy</a>
                            <a href="<?php echo e(route('schedules.create')); ?>" class="block text-indigo-600 hover:text-indigo-900">Tạo lịch mới</a>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <?php if($user->isAdmin()): ?>
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Quản lý nhân viên</h3>
                        <div class="space-y-2">
                            <a href="<?php echo e(route('admin.users.index')); ?>" class="block text-indigo-600 hover:text-indigo-900">Danh sách nhân viên</a>
                            <a href="<?php echo e(route('admin.users.create')); ?>" class="block text-indigo-600 hover:text-indigo-900">Thêm nhân viên</a>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Quản lý tài chính</h3>
                        <div class="space-y-2">
                            <a href="<?php echo e(route('admin.finances.index')); ?>" class="block text-indigo-600 hover:text-indigo-900">Sổ thu chi</a>
                            <a href="<?php echo e(route('admin.finances.create')); ?>" class="block text-indigo-600 hover:text-indigo-900">Ghi thu chi</a>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Quản lý tài sản</h3>
                        <div class="space-y-2">
                            <a href="<?php echo e(route('admin.assets.index')); ?>" class="block text-indigo-600 hover:text-indigo-900">Danh sách tài sản</a>
                            <a href="<?php echo e(route('admin.assets.create')); ?>" class="block text-indigo-600 hover:text-indigo-900">Thêm tài sản</a>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <?php if($user->isTeacher()): ?>
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Lớp học của tôi</h3>
                        <?php if(isset($myClasses) && $myClasses->count() > 0): ?>
                            <div class="space-y-2">
                                <?php $__currentLoopData = $myClasses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $class): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="flex justify-between items-center">
                                    <span><?php echo e($class->name); ?></span>
                                    <span class="text-sm text-gray-500"><?php echo e($class->active_students_count); ?> học sinh</span>
                                </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        <?php else: ?>
                            <p class="text-gray-500">Chưa được phân công lớp nào.</p>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Lịch dạy của tôi</h3>
                        <div class="space-y-2">
                            <a href="<?php echo e(route('schedules.my')); ?>" class="block text-indigo-600 hover:text-indigo-900">Xem lịch dạy</a>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH /Volumes/MOXC/Vscode/class-management-system/resources/views/dashboard.blade.php ENDPATH**/ ?>