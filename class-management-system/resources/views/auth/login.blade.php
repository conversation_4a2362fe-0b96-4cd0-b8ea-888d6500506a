<!DOCTYPE html>
<html lang="vi" class="h-full">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>Đ<PERSON>ng nhập - <PERSON><PERSON> thống quản lý lớp học</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])

    <style>
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInLeft {
            from {
                opacity: 0;
                transform: translateX(-50px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(50px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0px);
            }
            50% {
                transform: translateY(-10px);
            }
        }

        @keyframes bounce {
            0%, 20%, 53%, 80%, 100% {
                transform: translate3d(0,0,0);
            }
            40%, 43% {
                transform: translate3d(0, -30px, 0);
            }
            70% {
                transform: translate3d(0, -15px, 0);
            }
            90% {
                transform: translate3d(0, -4px, 0);
            }
        }

        .animate-fadeInUp {
            animation: fadeInUp 0.8s ease-out;
        }

        .animate-slideInLeft {
            animation: slideInLeft 0.8s ease-out;
        }

        .animate-slideInRight {
            animation: slideInRight 0.8s ease-out;
        }

        .animate-pulse-custom {
            animation: pulse 2s infinite;
        }

        .animate-float {
            animation: float 3s ease-in-out infinite;
        }

        .animate-bounce-custom {
            animation: bounce 2s infinite;
        }

        .gradient-bg {
            background: linear-gradient(135deg, #ff7e5f 0%, #feb47b 50%, #ffffff 100%);
        }

        .glass-effect {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 126, 95, 0.2);
        }

        .input-focus {
            transition: all 0.3s ease;
        }

        .input-focus:focus {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(255, 126, 95, 0.2);
            border-color: #ff7e5f;
        }

        .btn-hover {
            transition: all 0.3s ease;
            background: linear-gradient(135deg, #ff7e5f 0%, #feb47b 100%);
        }

        .btn-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(255, 126, 95, 0.4);
            background: linear-gradient(135deg, #feb47b 0%, #ff7e5f 100%);
        }

        .orange-gradient {
            background: linear-gradient(135deg, #ff7e5f 0%, #feb47b 100%);
        }
    </style>
</head>
<body class="h-full gradient-bg">
    <div class="min-h-full flex">
        <!-- Left Panel - Welcome Section -->
        <div class="flex-1 flex flex-col justify-center py-12 px-4 sm:px-6 lg:flex-none lg:px-20 xl:px-24">
            <div class="mx-auto w-full max-w-sm lg:w-96">
                <!-- Logo and Title -->
                <div class="text-center animate-slideInLeft">
                    <div class="animate-bounce-custom">
                        <div class="mx-auto h-24 w-24 orange-gradient rounded-full flex items-center justify-center shadow-2xl mb-6">
                            <svg class="h-14 w-14 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                            </svg>
                        </div>
                    </div>
                    <h2 class="text-4xl font-bold text-gray-800 mb-2">Chào mừng trở lại!</h2>
                    <p class="text-xl text-orange-600 font-medium">Hệ thống quản lý lớp học</p>
                </div>

                <!-- Login Form -->
                <div class="mt-10 animate-fadeInUp">
                    <div class="glass-effect rounded-3xl p-10 shadow-2xl">
                        <!-- Session Status -->
                        @if (session('status'))
                            <div class="mb-6 p-4 bg-green-50 border border-green-200 text-green-800 rounded-xl animate-slideInRight">
                                {{ session('status') }}
                            </div>
                        @endif

                        <form method="POST" action="{{ route('login') }}" class="space-y-8">
                            @csrf

                            <!-- Email Address -->
                            <div>
                                <label for="email" class="block text-sm font-semibold text-gray-700 mb-3">
                                    📧 Email
                                </label>
                                <div class="relative">
                                    <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                        <svg class="h-5 w-5 text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"></path>
                                        </svg>
                                    </div>
                                    <input id="email" name="email" type="email" value="{{ old('email') }}" required autofocus
                                           class="input-focus block w-full pl-12 pr-4 py-4 border-2 border-orange-200 rounded-xl bg-white placeholder-gray-400 text-gray-900 focus:outline-none focus:ring-2 focus:ring-orange-500"
                                           placeholder="Nhập email của bạn">
                                </div>
                                @error('email')
                                    <p class="mt-2 text-sm text-red-500">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Password -->
                            <div>
                                <label for="password" class="block text-sm font-semibold text-gray-700 mb-3">
                                    🔒 Mật khẩu
                                </label>
                                <div class="relative">
                                    <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                        <svg class="h-5 w-5 text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                                        </svg>
                                    </div>
                                    <input id="password" name="password" type="password" required
                                           class="input-focus block w-full pl-12 pr-4 py-4 border-2 border-orange-200 rounded-xl bg-white placeholder-gray-400 text-gray-900 focus:outline-none focus:ring-2 focus:ring-orange-500"
                                           placeholder="Nhập mật khẩu">
                                </div>
                                @error('password')
                                    <p class="mt-2 text-sm text-red-500">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Remember Me -->
                            <div class="flex items-center">
                                <input id="remember_me" name="remember" type="checkbox"
                                       class="h-5 w-5 text-orange-500 focus:ring-orange-500 border-orange-300 rounded">
                                <label for="remember_me" class="ml-3 block text-sm font-medium text-gray-700">
                                    Ghi nhớ đăng nhập
                                </label>
                            </div>

                            <!-- Submit Button -->
                            <div>
                                <button type="submit"
                                        class="btn-hover w-full flex justify-center py-4 px-6 border border-transparent rounded-xl shadow-lg text-lg font-semibold text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500">
                                    <span class="flex items-center">
                                        <svg class="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"></path>
                                        </svg>
                                        Đăng nhập
                                    </span>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Right Panel - Demo Accounts -->
        <div class="hidden lg:block relative w-0 flex-1">
            <div class="absolute inset-0 h-full w-full bg-white flex items-center justify-center">
                <div class="text-center animate-slideInRight p-8">
                    <div class="animate-float mb-10">
                        <div class="mx-auto h-40 w-40 orange-gradient rounded-full flex items-center justify-center shadow-2xl">
                            <svg class="h-20 w-20 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                            </svg>
                        </div>
                    </div>

                    <h3 class="text-3xl font-bold mb-8 text-gray-800">Tài khoản demo</h3>
                    <p class="text-gray-600 mb-8">Sử dụng các tài khoản sau để trải nghiệm hệ thống</p>

                    <div class="space-y-6 max-w-sm mx-auto">
                        <div class="bg-gradient-to-r from-yellow-50 to-orange-50 border-2 border-orange-200 rounded-2xl p-6 text-left shadow-lg hover:shadow-xl transition-all duration-300">
                            <h4 class="font-bold text-orange-600 mb-3 text-lg">👑 Admin</h4>
                            <p class="text-gray-700 font-medium"><EMAIL></p>
                            <p class="text-gray-700 font-medium">admin123</p>
                        </div>

                        <div class="bg-gradient-to-r from-green-50 to-orange-50 border-2 border-orange-200 rounded-2xl p-6 text-left shadow-lg hover:shadow-xl transition-all duration-300">
                            <h4 class="font-bold text-orange-600 mb-3 text-lg">👨‍💼 Quản sinh</h4>
                            <p class="text-gray-700 font-medium"><EMAIL></p>
                            <p class="text-gray-700 font-medium">manager123</p>
                        </div>

                        <div class="bg-gradient-to-r from-blue-50 to-orange-50 border-2 border-orange-200 rounded-2xl p-6 text-left shadow-lg hover:shadow-xl transition-all duration-300">
                            <h4 class="font-bold text-orange-600 mb-3 text-lg">👨‍🏫 Giáo viên</h4>
                            <p class="text-gray-700 font-medium"><EMAIL></p>
                            <p class="text-gray-700 font-medium">teacher123</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
