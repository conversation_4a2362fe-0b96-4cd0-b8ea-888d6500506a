<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                <PERSON><PERSON> thu chi mới
            </h2>
            <a href="{{ route('admin.finances.index') }}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                Quay lại
            </a>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    <form method="POST" action="{{ route('admin.finances.store') }}">
                        @csrf

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Loại giao dịch -->
                            <div>
                                <label for="type" class="block text-sm font-medium text-gray-700"><PERSON>ại giao dịch</label>
                                <select name="type" id="type" required
                                        class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                    <option value="">Chọn loại giao dịch</option>
                                    <option value="income" {{ old('type') === 'income' ? 'selected' : '' }}>Thu</option>
                                    <option value="expense" {{ old('type') === 'expense' ? 'selected' : '' }}>Chi</option>
                                </select>
                                @error('type')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Danh mục -->
                            <div>
                                <label for="category" class="block text-sm font-medium text-gray-700">Danh mục</label>
                                <input type="text" name="category" id="category" value="{{ old('category') }}" required
                                       placeholder="VD: Học phí, Lương, Vật tư..."
                                       class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                @error('category')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Số tiền -->
                            <div>
                                <label for="amount" class="block text-sm font-medium text-gray-700">Số tiền (VNĐ)</label>
                                <input type="number" name="amount" id="amount" value="{{ old('amount') }}" required
                                       min="0" step="1000"
                                       class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                @error('amount')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Ngày giao dịch -->
                            <div>
                                <label for="transaction_date" class="block text-sm font-medium text-gray-700">Ngày giao dịch</label>
                                <input type="date" name="transaction_date" id="transaction_date" 
                                       value="{{ old('transaction_date', date('Y-m-d')) }}" required
                                       class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                @error('transaction_date')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Số tham chiếu -->
                            <div>
                                <label for="reference_number" class="block text-sm font-medium text-gray-700">Số tham chiếu (tùy chọn)</label>
                                <input type="text" name="reference_number" id="reference_number" value="{{ old('reference_number') }}"
                                       placeholder="VD: HD001, REF123..."
                                       class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                @error('reference_number')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>

                        <!-- Mô tả -->
                        <div class="mt-6">
                            <label for="description" class="block text-sm font-medium text-gray-700">Mô tả</label>
                            <textarea name="description" id="description" rows="3" required
                                      placeholder="Mô tả chi tiết về giao dịch..."
                                      class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">{{ old('description') }}</textarea>
                            @error('description')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Ghi chú -->
                        <div class="mt-6">
                            <label for="notes" class="block text-sm font-medium text-gray-700">Ghi chú (tùy chọn)</label>
                            <textarea name="notes" id="notes" rows="2"
                                      placeholder="Ghi chú thêm..."
                                      class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">{{ old('notes') }}</textarea>
                            @error('notes')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div class="mt-6 flex justify-end">
                            <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                Ghi nhận giao dịch
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
