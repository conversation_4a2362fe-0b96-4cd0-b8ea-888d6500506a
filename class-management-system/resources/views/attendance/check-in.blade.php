<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            Đ<PERSON><PERSON><PERSON> danh - {{ $schedule->class->name }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Thông tin lịch dạy -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Thông tin lịch dạy</h3>
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div>
                            <span class="text-sm font-medium text-gray-500">Lớp:</span>
                            <p class="text-sm text-gray-900">{{ $schedule->class->name }}</p>
                        </div>
                        <div>
                            <span class="text-sm font-medium text-gray-500">Ngày:</span>
                            <p class="text-sm text-gray-900">{{ $schedule->date->format('d/m/Y') }}</p>
                        </div>
                        <div>
                            <span class="text-sm font-medium text-gray-500">Khung giờ:</span>
                            <p class="text-sm text-gray-900">{{ $schedule->timeSlot->name }}</p>
                        </div>
                        <div>
                            <span class="text-sm font-medium text-gray-500">Thời gian:</span>
                            <p class="text-sm text-gray-900">{{ $schedule->timeSlot->start_time }} - {{ $schedule->timeSlot->end_time }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Form điểm danh -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Điểm danh học sinh</h3>
                    
                    <form method="POST" action="{{ route('attendance.store-check-in', $schedule) }}">
                        @csrf
                        
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mã HS</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tên học sinh</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Trạng thái</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ghi chú</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    @foreach($schedule->class->activeStudents as $index => $student)
                                    @php
                                        $existingAttendance = $schedule->attendance->where('student_id', $student->id)->first();
                                    @endphp
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                            {{ $student->student_code }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            {{ $student->name }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <input type="hidden" name="attendances[{{ $index }}][student_id]" value="{{ $student->id }}">
                                            <select name="attendances[{{ $index }}][status]" required
                                                    class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                                <option value="present" {{ $existingAttendance && $existingAttendance->status === 'present' ? 'selected' : '' }}>Có mặt</option>
                                                <option value="absent" {{ $existingAttendance && $existingAttendance->status === 'absent' ? 'selected' : '' }}>Vắng</option>
                                                <option value="late" {{ $existingAttendance && $existingAttendance->status === 'late' ? 'selected' : '' }}>Muộn</option>
                                            </select>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <input type="text" name="attendances[{{ $index }}][notes]" 
                                                   value="{{ $existingAttendance ? $existingAttendance->notes : '' }}"
                                                   placeholder="Ghi chú..."
                                                   class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>

                        <div class="mt-6 flex justify-between">
                            <a href="{{ route('schedules.my') }}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                                Quay lại
                            </a>
                            <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                Lưu điểm danh
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
