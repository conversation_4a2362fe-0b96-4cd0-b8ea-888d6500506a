<x-app-layout>
    <x-slot name="header">
        <div class="flex items-center space-x-4">
            <div class="h-12 w-12 orange-gradient rounded-full flex items-center justify-center shadow-lg">
                <svg class="h-7 w-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2 2z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V5a2 2 0 012-2h4a2 2 0 012 2v2m-6 4h4"></path>
                </svg>
            </div>
            <div>
                <h2 class="font-bold text-2xl text-gray-800 leading-tight">
                    Trang chủ
                </h2>
                <p class="text-orange-600 font-medium">{{ $user->name }} ({{ ucfirst($user->role) }})</p>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Thống kê tổng quan -->
            @if($user->isAdmin() || $user->isManager())
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                <div class="card-hover bg-white overflow-hidden shadow-xl sm:rounded-2xl border-l-4 border-orange-400">
                    <div class="p-8">
                        <div class="flex items-center">
                            <div class="p-4 rounded-full bg-orange-100 text-orange-600">
                                <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                                </svg>
                            </div>
                            <div class="ml-6">
                                <p class="text-sm font-semibold text-gray-600 uppercase tracking-wide">Tổng học sinh</p>
                                <p class="text-3xl font-bold text-gray-900">{{ $totalStudents }}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card-hover bg-white overflow-hidden shadow-xl sm:rounded-2xl border-l-4 border-green-400">
                    <div class="p-8">
                        <div class="flex items-center">
                            <div class="p-4 rounded-full bg-green-100 text-green-600">
                                <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                </svg>
                            </div>
                            <div class="ml-6">
                                <p class="text-sm font-semibold text-gray-600 uppercase tracking-wide">Tổng lớp học</p>
                                <p class="text-3xl font-bold text-gray-900">{{ $totalClasses }}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card-hover bg-white overflow-hidden shadow-xl sm:rounded-2xl border-l-4 border-purple-400">
                    <div class="p-8">
                        <div class="flex items-center">
                            <div class="p-4 rounded-full bg-purple-100 text-purple-600">
                                <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                </svg>
                            </div>
                            <div class="ml-6">
                                <p class="text-sm font-semibold text-gray-600 uppercase tracking-wide">Tổng giáo viên</p>
                                <p class="text-3xl font-bold text-gray-900">{{ $totalTeachers }}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card-hover bg-white overflow-hidden shadow-xl sm:rounded-2xl border-l-4 border-red-400">
                    <div class="p-8">
                        <div class="flex items-center">
                            <div class="p-4 rounded-full bg-red-100 text-red-600">
                                <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                </svg>
                            </div>
                            <div class="ml-6">
                                <p class="text-sm font-semibold text-gray-600 uppercase tracking-wide">Vắng tháng này</p>
                                <p class="text-3xl font-bold text-gray-900">{{ $monthlyAbsent }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            @endif

            <!-- Lịch dạy hôm nay -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">
                        @if($user->isTeacher())
                            Lịch dạy của tôi hôm nay
                        @else
                            Lịch dạy hôm nay ({{ $today->format('d/m/Y') }})
                        @endif
                    </h3>

                    @if($todaySchedules->count() > 0)
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Khung giờ</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Lớp</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Giáo viên</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Thời gian</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Hành động</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    @foreach($todaySchedules as $schedule)
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                            {{ $schedule->timeSlot->name }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            {{ $schedule->class->name }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            {{ $schedule->teacher->name }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            {{ $schedule->timeSlot->start_time }} - {{ $schedule->timeSlot->end_time }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            @if($user->isTeacher() && $schedule->teacher_id == $user->id)
                                                <a href="{{ route('attendance.check-in', $schedule) }}" class="text-indigo-600 hover:text-indigo-900">Điểm danh</a>
                                            @elseif($user->isAdmin() || $user->isManager())
                                                <a href="{{ route('attendance.show', $schedule) }}" class="text-indigo-600 hover:text-indigo-900">Xem điểm danh</a>
                                            @endif
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <p class="text-gray-500">Không có lịch dạy nào hôm nay.</p>
                    @endif
                </div>
            </div>

            <!-- Menu chức năng -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                @if($user->isAdmin() || $user->isManager())
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Quản lý học sinh</h3>
                        <div class="space-y-2">
                            <a href="{{ route('students.index') }}" class="block text-indigo-600 hover:text-indigo-900">Danh sách học sinh</a>
                            <a href="{{ route('students.create') }}" class="block text-indigo-600 hover:text-indigo-900">Thêm học sinh mới</a>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Quản lý lớp học</h3>
                        <div class="space-y-2">
                            <a href="{{ route('classes.index') }}" class="block text-indigo-600 hover:text-indigo-900">Danh sách lớp học</a>
                            <a href="{{ route('classes.create') }}" class="block text-indigo-600 hover:text-indigo-900">Tạo lớp mới</a>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Lịch dạy</h3>
                        <div class="space-y-2">
                            <a href="{{ route('schedules.index') }}" class="block text-indigo-600 hover:text-indigo-900">Xem lịch dạy</a>
                            <a href="{{ route('schedules.create') }}" class="block text-indigo-600 hover:text-indigo-900">Tạo lịch mới</a>
                        </div>
                    </div>
                </div>
                @endif

                @if($user->isAdmin())
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Quản lý nhân viên</h3>
                        <div class="space-y-2">
                            <a href="{{ route('admin.users.index') }}" class="block text-indigo-600 hover:text-indigo-900">Danh sách nhân viên</a>
                            <a href="{{ route('admin.users.create') }}" class="block text-indigo-600 hover:text-indigo-900">Thêm nhân viên</a>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Quản lý tài chính</h3>
                        <div class="space-y-2">
                            <a href="{{ route('admin.finances.index') }}" class="block text-indigo-600 hover:text-indigo-900">Sổ thu chi</a>
                            <a href="{{ route('admin.finances.create') }}" class="block text-indigo-600 hover:text-indigo-900">Ghi thu chi</a>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Quản lý tài sản</h3>
                        <div class="space-y-2">
                            <a href="{{ route('admin.assets.index') }}" class="block text-indigo-600 hover:text-indigo-900">Danh sách tài sản</a>
                            <a href="{{ route('admin.assets.create') }}" class="block text-indigo-600 hover:text-indigo-900">Thêm tài sản</a>
                        </div>
                    </div>
                </div>
                @endif

                @if($user->isTeacher())
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Lớp học của tôi</h3>
                        @if(isset($myClasses) && $myClasses->count() > 0)
                            <div class="space-y-2">
                                @foreach($myClasses as $class)
                                <div class="flex justify-between items-center">
                                    <span>{{ $class->name }}</span>
                                    <span class="text-sm text-gray-500">{{ $class->active_students_count }} học sinh</span>
                                </div>
                                @endforeach
                            </div>
                        @else
                            <p class="text-gray-500">Chưa được phân công lớp nào.</p>
                        @endif
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Lịch dạy của tôi</h3>
                        <div class="space-y-2">
                            <a href="{{ route('schedules.my') }}" class="block text-indigo-600 hover:text-indigo-900">Xem lịch dạy</a>
                        </div>
                    </div>
                </div>
                @endif
            </div>
        </div>
    </div>
</x-app-layout>
