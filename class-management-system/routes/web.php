<?php

use App\Http\Controllers\ProfileController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\StudentController;
use App\Http\Controllers\ClassRoomController;
use App\Http\Controllers\ScheduleController;
use App\Http\Controllers\AttendanceController;
use App\Http\Controllers\TimeSlotController;
use App\Http\Controllers\NotificationController;
use App\Http\Controllers\Admin\UserController;
use App\Http\Controllers\Admin\AssetController;
use App\Http\Controllers\Admin\FinanceController;
use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return view('welcome');
});

Route::get('/dashboard', [DashboardController::class, 'index'])
    ->middleware(['auth', 'verified'])
    ->name('dashboard');

Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
});

// Routes cho Admin và Manager
Route::middleware(['auth', 'role:admin,manager'])->group(function () {
    // Quản lý học sinh
    Route::resource('students', StudentController::class);

    // Quản lý lớp học
    Route::resource('classes', ClassRoomController::class);

    // Quản lý lịch dạy
    Route::resource('schedules', ScheduleController::class);

    // Quản lý khung giờ
    Route::resource('time-slots', TimeSlotController::class);

    // Điểm danh
    Route::resource('attendance', AttendanceController::class);
    Route::post('attendance/bulk-create/{schedule}', [AttendanceController::class, 'bulkCreate'])->name('attendance.bulk-create');

    // Thông báo
    Route::resource('notifications', NotificationController::class);
    Route::post('notifications/{notification}/send', [NotificationController::class, 'send'])->name('notifications.send');
});

// Routes chỉ cho Admin
Route::middleware(['auth', 'role:admin'])->group(function () {
    // Quản lý nhân viên
    Route::resource('admin/users', UserController::class);

    // Quản lý tài sản
    Route::resource('admin/assets', AssetController::class);
    Route::post('admin/assets/{asset}/assign', [AssetController::class, 'assign'])->name('admin.assets.assign');

    // Quản lý tài chính
    Route::resource('admin/finances', FinanceController::class);
});

// Routes cho Teacher
Route::middleware(['auth', 'role:teacher'])->group(function () {
    // Xem lịch dạy của mình
    Route::get('my-schedules', [ScheduleController::class, 'mySchedules'])->name('schedules.my');

    // Check-in học sinh
    Route::get('attendance/check-in/{schedule}', [AttendanceController::class, 'checkIn'])->name('attendance.check-in');
    Route::post('attendance/check-in/{schedule}', [AttendanceController::class, 'storeCheckIn'])->name('attendance.store-check-in');
});

require __DIR__.'/auth.php';
