<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Student extends Model
{
    use HasFactory;

    protected $fillable = [
        'student_code',
        'name',
        'birth_date',
        'parent_name',
        'parent_phone',
        'google_drive_link',
        'status',
    ];

    protected function casts(): array
    {
        return [
            'birth_date' => 'date',
        ];
    }

    // Relationships
    public function classes()
    {
        return $this->belongsToMany(ClassRoom::class, 'class_student', 'student_id', 'class_id')
                    ->withPivot('enrolled_at', 'left_at', 'status')
                    ->withTimestamps();
    }

    public function currentClass()
    {
        return $this->belongsToMany(ClassRoom::class, 'class_student', 'student_id', 'class_id')
                    ->wherePivot('status', 'active')
                    ->withPivot('enrolled_at', 'left_at', 'status')
                    ->withTimestamps()
                    ->first();
    }

    public function attendance()
    {
        return $this->hasMany(Attendance::class);
    }

    // Helper methods
    public static function generateStudentCode()
    {
        $year = date('Y');
        $lastStudent = self::whereYear('created_at', $year)
                          ->orderBy('id', 'desc')
                          ->first();

        $number = $lastStudent ? (int)substr($lastStudent->student_code, -4) + 1 : 1;

        return $year . str_pad($number, 4, '0', STR_PAD_LEFT);
    }
}
