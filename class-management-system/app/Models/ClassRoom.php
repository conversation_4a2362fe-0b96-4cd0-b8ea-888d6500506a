<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ClassRoom extends Model
{
    use HasFactory;

    protected $table = 'classes';

    protected $fillable = [
        'name',
        'description',
        'teacher_id',
        'max_students',
        'status',
    ];

    // Relationships
    public function teacher()
    {
        return $this->belongsTo(User::class, 'teacher_id');
    }

    public function students()
    {
        return $this->belongsToMany(Student::class, 'class_student', 'class_id', 'student_id')
                    ->withPivot('enrolled_at', 'left_at', 'status')
                    ->withTimestamps();
    }

    public function activeStudents()
    {
        return $this->belongsToMany(Student::class, 'class_student', 'class_id', 'student_id')
                    ->wherePivot('status', 'active')
                    ->withPivot('enrolled_at', 'left_at', 'status')
                    ->withTimestamps();
    }

    public function schedules()
    {
        return $this->hasMany(Schedule::class, 'class_id');
    }

    // Helper methods
    public function getActiveStudentsCount()
    {
        return $this->activeStudents()->count();
    }

    public function canAddMoreStudents()
    {
        return $this->getActiveStudentsCount() < $this->max_students;
    }
}
