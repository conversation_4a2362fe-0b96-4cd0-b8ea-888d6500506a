<?php

namespace App\Http\Controllers;

use App\Models\Schedule;
use App\Models\ClassRoom;
use App\Models\User;
use App\Models\TimeSlot;
use Illuminate\Http\Request;
use Carbon\Carbon;

class ScheduleController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $schedules = Schedule::with(['class', 'teacher', 'timeSlot'])
            ->orderBy('date', 'desc')
            ->orderBy('time_slot_id')
            ->paginate(15);

        return view('schedules.index', compact('schedules'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $classes = ClassRoom::where('status', 'active')->get();
        $teachers = User::where('role', 'teacher')->where('status', 'active')->get();
        $timeSlots = TimeSlot::where('status', 'active')->get();

        return view('schedules.create', compact('classes', 'teachers', 'timeSlots'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'class_id' => 'required|exists:classes,id',
            'teacher_id' => 'required|exists:users,id',
            'time_slot_id' => 'required|exists:time_slots,id',
            'date' => 'required|date|after_or_equal:today',
            'notes' => 'nullable|string|max:1000',
        ]);

        // Kiểm tra conflict
        $existingSchedule = Schedule::where('teacher_id', $request->teacher_id)
            ->where('time_slot_id', $request->time_slot_id)
            ->where('date', $request->date)
            ->where('status', '!=', 'cancelled')
            ->first();

        if ($existingSchedule) {
            return back()->withErrors(['conflict' => 'Giáo viên đã có lịch dạy trong khung giờ này.']);
        }

        $classSchedule = Schedule::where('class_id', $request->class_id)
            ->where('time_slot_id', $request->time_slot_id)
            ->where('date', $request->date)
            ->where('status', '!=', 'cancelled')
            ->first();

        if ($classSchedule) {
            return back()->withErrors(['conflict' => 'Lớp học đã có lịch dạy trong khung giờ này.']);
        }

        Schedule::create([
            'class_id' => $request->class_id,
            'teacher_id' => $request->teacher_id,
            'time_slot_id' => $request->time_slot_id,
            'date' => $request->date,
            'notes' => $request->notes,
            'status' => 'scheduled',
        ]);

        return redirect()->route('schedules.index')
            ->with('success', 'Lịch dạy đã được tạo thành công.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Schedule $schedule)
    {
        $schedule->load(['class.activeStudents', 'teacher', 'timeSlot', 'attendance.student']);
        return view('schedules.show', compact('schedule'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Schedule $schedule)
    {
        $classes = ClassRoom::where('status', 'active')->get();
        $teachers = User::where('role', 'teacher')->where('status', 'active')->get();
        $timeSlots = TimeSlot::where('status', 'active')->get();

        return view('schedules.edit', compact('schedule', 'classes', 'teachers', 'timeSlots'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Schedule $schedule)
    {
        $request->validate([
            'class_id' => 'required|exists:classes,id',
            'teacher_id' => 'required|exists:users,id',
            'time_slot_id' => 'required|exists:time_slots,id',
            'date' => 'required|date',
            'notes' => 'nullable|string|max:1000',
            'status' => 'required|in:scheduled,completed,cancelled',
        ]);

        // Kiểm tra conflict (trừ schedule hiện tại)
        $existingSchedule = Schedule::where('teacher_id', $request->teacher_id)
            ->where('time_slot_id', $request->time_slot_id)
            ->where('date', $request->date)
            ->where('status', '!=', 'cancelled')
            ->where('id', '!=', $schedule->id)
            ->first();

        if ($existingSchedule) {
            return back()->withErrors(['conflict' => 'Giáo viên đã có lịch dạy trong khung giờ này.']);
        }

        $schedule->update([
            'class_id' => $request->class_id,
            'teacher_id' => $request->teacher_id,
            'time_slot_id' => $request->time_slot_id,
            'date' => $request->date,
            'notes' => $request->notes,
            'status' => $request->status,
        ]);

        return redirect()->route('schedules.index')
            ->with('success', 'Lịch dạy đã được cập nhật.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Schedule $schedule)
    {
        $schedule->delete();

        return redirect()->route('schedules.index')
            ->with('success', 'Lịch dạy đã được xóa.');
    }

    /**
     * Lịch dạy của giáo viên
     */
    public function mySchedules()
    {
        $schedules = Schedule::with(['class', 'timeSlot'])
            ->where('teacher_id', auth()->id())
            ->where('date', '>=', Carbon::today())
            ->orderBy('date')
            ->orderBy('time_slot_id')
            ->paginate(10);

        return view('schedules.my', compact('schedules'));
    }
}
