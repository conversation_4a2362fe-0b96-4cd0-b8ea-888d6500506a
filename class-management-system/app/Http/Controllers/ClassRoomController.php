<?php

namespace App\Http\Controllers;

use App\Models\ClassRoom;
use App\Models\User;
use App\Models\Student;
use Illuminate\Http\Request;

class ClassRoomController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $classes = ClassRoom::with(['teacher', 'activeStudents'])
            ->withCount('activeStudents')
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return view('classes.index', compact('classes'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $teachers = User::where('role', 'teacher')
            ->where('status', 'active')
            ->get();

        return view('classes.create', compact('teachers'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'teacher_id' => 'nullable|exists:users,id',
            'max_students' => 'required|integer|min:1|max:50',
        ]);

        ClassRoom::create([
            'name' => $request->name,
            'description' => $request->description,
            'teacher_id' => $request->teacher_id,
            'max_students' => $request->max_students,
            'status' => 'active',
        ]);

        return redirect()->route('classes.index')
            ->with('success', 'Lớp học đã được tạo thành công.');
    }

    /**
     * Display the specified resource.
     */
    public function show(ClassRoom $class)
    {
        $class->load(['teacher', 'activeStudents', 'schedules.timeSlot']);
        return view('classes.show', compact('class'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(ClassRoom $class)
    {
        $teachers = User::where('role', 'teacher')
            ->where('status', 'active')
            ->get();

        return view('classes.edit', compact('class', 'teachers'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, ClassRoom $class)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'teacher_id' => 'nullable|exists:users,id',
            'max_students' => 'required|integer|min:1|max:50',
            'status' => 'required|in:active,inactive',
        ]);

        $class->update([
            'name' => $request->name,
            'description' => $request->description,
            'teacher_id' => $request->teacher_id,
            'max_students' => $request->max_students,
            'status' => $request->status,
        ]);

        return redirect()->route('classes.index')
            ->with('success', 'Thông tin lớp học đã được cập nhật.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(ClassRoom $class)
    {
        // Kiểm tra xem lớp có học sinh không
        if ($class->activeStudents()->count() > 0) {
            return redirect()->route('classes.index')
                ->with('error', 'Không thể xóa lớp học đang có học sinh.');
        }

        $class->delete();

        return redirect()->route('classes.index')
            ->with('success', 'Lớp học đã được xóa.');
    }

    /**
     * Thêm học sinh vào lớp
     */
    public function addStudent(Request $request, ClassRoom $class)
    {
        $request->validate([
            'student_id' => 'required|exists:students,id',
        ]);

        $student = Student::find($request->student_id);

        // Kiểm tra học sinh đã có lớp chưa
        if ($student->currentClass()) {
            return back()->with('error', 'Học sinh đã có lớp học.');
        }

        // Kiểm tra lớp còn chỗ không
        if (!$class->canAddMoreStudents()) {
            return back()->with('error', 'Lớp học đã đầy.');
        }

        $student->classes()->attach($class->id, [
            'enrolled_at' => now()->toDateString(),
            'status' => 'active',
        ]);

        return back()->with('success', 'Đã thêm học sinh vào lớp.');
    }

    /**
     * Xóa học sinh khỏi lớp
     */
    public function removeStudent(ClassRoom $class, Student $student)
    {
        $student->classes()->updateExistingPivot($class->id, [
            'status' => 'inactive',
            'left_at' => now()->toDateString(),
        ]);

        return back()->with('success', 'Đã xóa học sinh khỏi lớp.');
    }
}
