<?php

namespace App\Http\Controllers;

use App\Models\TimeSlot;
use Illuminate\Http\Request;

class TimeSlotController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $timeSlots = TimeSlot::orderBy('period')
            ->orderBy('start_time')
            ->paginate(10);

        return view('time-slots.index', compact('timeSlots'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('time-slots.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'period' => 'required|in:morning,afternoon,evening',
            'start_time' => 'required|date_format:H:i',
            'end_time' => 'required|date_format:H:i|after:start_time',
        ]);

        TimeSlot::create([
            'name' => $request->name,
            'period' => $request->period,
            'start_time' => $request->start_time,
            'end_time' => $request->end_time,
            'status' => 'active',
        ]);

        return redirect()->route('time-slots.index')
            ->with('success', 'Khung giờ đã được tạo thành công.');
    }

    /**
     * Display the specified resource.
     */
    public function show(TimeSlot $timeSlot)
    {
        $timeSlot->load('schedules.class', 'schedules.teacher');
        return view('time-slots.show', compact('timeSlot'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(TimeSlot $timeSlot)
    {
        return view('time-slots.edit', compact('timeSlot'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, TimeSlot $timeSlot)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'period' => 'required|in:morning,afternoon,evening',
            'start_time' => 'required|date_format:H:i',
            'end_time' => 'required|date_format:H:i|after:start_time',
            'status' => 'required|in:active,inactive',
        ]);

        $timeSlot->update([
            'name' => $request->name,
            'period' => $request->period,
            'start_time' => $request->start_time,
            'end_time' => $request->end_time,
            'status' => $request->status,
        ]);

        return redirect()->route('time-slots.index')
            ->with('success', 'Khung giờ đã được cập nhật.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(TimeSlot $timeSlot)
    {
        // Kiểm tra xem có lịch dạy nào đang sử dụng không
        if ($timeSlot->schedules()->where('status', '!=', 'cancelled')->count() > 0) {
            return redirect()->route('time-slots.index')
                ->with('error', 'Không thể xóa khung giờ đang được sử dụng.');
        }

        $timeSlot->delete();

        return redirect()->route('time-slots.index')
            ->with('success', 'Khung giờ đã được xóa.');
    }
}
