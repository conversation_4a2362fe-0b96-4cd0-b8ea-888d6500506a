<?php

namespace App\Http\Controllers;

use App\Models\Attendance;
use App\Models\Schedule;
use App\Models\Student;
use Illuminate\Http\Request;

class AttendanceController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $attendances = Attendance::with(['schedule.class', 'schedule.teacher', 'student'])
            ->orderBy('created_at', 'desc')
            ->paginate(15);

        return view('attendance.index', compact('attendances'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $schedules = Schedule::with(['class', 'teacher', 'timeSlot'])
            ->where('status', 'scheduled')
            ->where('date', '>=', now()->toDateString())
            ->get();

        return view('attendance.create', compact('schedules'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'schedule_id' => 'required|exists:schedules,id',
            'student_id' => 'required|exists:students,id',
            'status' => 'required|in:present,absent,late',
            'notes' => 'nullable|string|max:500',
        ]);

        // Kiểm tra đã điểm danh chưa
        $existing = Attendance::where('schedule_id', $request->schedule_id)
            ->where('student_id', $request->student_id)
            ->first();

        if ($existing) {
            return back()->withErrors(['duplicate' => 'Học sinh này đã được điểm danh.']);
        }

        Attendance::create([
            'schedule_id' => $request->schedule_id,
            'student_id' => $request->student_id,
            'status' => $request->status,
            'notes' => $request->notes,
            'checked_in_at' => now(),
            'checked_by' => auth()->id(),
        ]);

        return redirect()->route('attendance.index')
            ->with('success', 'Điểm danh đã được ghi nhận.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Schedule $schedule)
    {
        $schedule->load(['class.activeStudents', 'teacher', 'timeSlot', 'attendance.student']);

        return view('attendance.show', compact('schedule'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Attendance $attendance)
    {
        return view('attendance.edit', compact('attendance'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Attendance $attendance)
    {
        $request->validate([
            'status' => 'required|in:present,absent,late',
            'notes' => 'nullable|string|max:500',
        ]);

        $attendance->update([
            'status' => $request->status,
            'notes' => $request->notes,
        ]);

        return redirect()->route('attendance.index')
            ->with('success', 'Điểm danh đã được cập nhật.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Attendance $attendance)
    {
        $attendance->delete();

        return redirect()->route('attendance.index')
            ->with('success', 'Điểm danh đã được xóa.');
    }

    /**
     * Tạo điểm danh hàng loạt cho một lịch dạy
     */
    public function bulkCreate(Request $request, Schedule $schedule)
    {
        $request->validate([
            'attendances' => 'required|array',
            'attendances.*.student_id' => 'required|exists:students,id',
            'attendances.*.status' => 'required|in:present,absent,late',
            'attendances.*.notes' => 'nullable|string|max:500',
        ]);

        foreach ($request->attendances as $attendanceData) {
            // Kiểm tra đã điểm danh chưa
            $existing = Attendance::where('schedule_id', $schedule->id)
                ->where('student_id', $attendanceData['student_id'])
                ->first();

            if (!$existing) {
                Attendance::create([
                    'schedule_id' => $schedule->id,
                    'student_id' => $attendanceData['student_id'],
                    'status' => $attendanceData['status'],
                    'notes' => $attendanceData['notes'] ?? null,
                    'checked_in_at' => now(),
                    'checked_by' => auth()->id(),
                ]);
            }
        }

        return redirect()->route('attendance.show', $schedule)
            ->with('success', 'Điểm danh hàng loạt đã được ghi nhận.');
    }

    /**
     * Check-in cho giáo viên
     */
    public function checkIn(Schedule $schedule)
    {
        // Kiểm tra quyền
        if (auth()->user()->isTeacher() && $schedule->teacher_id !== auth()->id()) {
            abort(403, 'Bạn không có quyền điểm danh lớp này.');
        }

        $schedule->load(['class.activeStudents', 'timeSlot', 'attendance.student']);

        return view('attendance.check-in', compact('schedule'));
    }

    /**
     * Lưu check-in
     */
    public function storeCheckIn(Request $request, Schedule $schedule)
    {
        // Kiểm tra quyền
        if (auth()->user()->isTeacher() && $schedule->teacher_id !== auth()->id()) {
            abort(403, 'Bạn không có quyền điểm danh lớp này.');
        }

        $request->validate([
            'attendances' => 'required|array',
            'attendances.*.student_id' => 'required|exists:students,id',
            'attendances.*.status' => 'required|in:present,absent,late',
            'attendances.*.notes' => 'nullable|string|max:500',
        ]);

        foreach ($request->attendances as $attendanceData) {
            Attendance::updateOrCreate(
                [
                    'schedule_id' => $schedule->id,
                    'student_id' => $attendanceData['student_id'],
                ],
                [
                    'status' => $attendanceData['status'],
                    'notes' => $attendanceData['notes'] ?? null,
                    'checked_in_at' => now(),
                    'checked_by' => auth()->id(),
                ]
            );
        }

        return redirect()->route('schedules.my')
            ->with('success', 'Điểm danh đã được hoàn thành.');
    }
}
