<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Finance;
use Illuminate\Http\Request;
use Carbon\Carbon;

class FinanceController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Finance::with('recorder')->orderBy('transaction_date', 'desc');

        // Filter by type
        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        // Filter by date range
        if ($request->filled('from_date')) {
            $query->where('transaction_date', '>=', $request->from_date);
        }

        if ($request->filled('to_date')) {
            $query->where('transaction_date', '<=', $request->to_date);
        }

        $finances = $query->paginate(15);

        // Calculate totals
        $totalIncome = Finance::income()->completed()->sum('amount');
        $totalExpense = Finance::expense()->completed()->sum('amount');
        $balance = $totalIncome - $totalExpense;

        return view('admin.finances.index', compact('finances', 'totalIncome', 'totalExpense', 'balance'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.finances.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'type' => 'required|in:income,expense',
            'category' => 'required|string|max:255',
            'description' => 'required|string|max:1000',
            'amount' => 'required|numeric|min:0',
            'transaction_date' => 'required|date',
            'reference_number' => 'nullable|string|max:255',
            'notes' => 'nullable|string|max:1000',
        ]);

        Finance::create([
            'type' => $request->type,
            'category' => $request->category,
            'description' => $request->description,
            'amount' => $request->amount,
            'transaction_date' => $request->transaction_date,
            'recorded_by' => auth()->id(),
            'reference_number' => $request->reference_number,
            'notes' => $request->notes,
            'status' => 'completed',
        ]);

        return redirect()->route('admin.finances.index')
            ->with('success', 'Giao dịch đã được ghi nhận thành công.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Finance $finance)
    {
        $finance->load('recorder');
        return view('admin.finances.show', compact('finance'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Finance $finance)
    {
        return view('admin.finances.edit', compact('finance'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Finance $finance)
    {
        $request->validate([
            'type' => 'required|in:income,expense',
            'category' => 'required|string|max:255',
            'description' => 'required|string|max:1000',
            'amount' => 'required|numeric|min:0',
            'transaction_date' => 'required|date',
            'reference_number' => 'nullable|string|max:255',
            'notes' => 'nullable|string|max:1000',
            'status' => 'required|in:pending,completed,cancelled',
        ]);

        $finance->update([
            'type' => $request->type,
            'category' => $request->category,
            'description' => $request->description,
            'amount' => $request->amount,
            'transaction_date' => $request->transaction_date,
            'reference_number' => $request->reference_number,
            'notes' => $request->notes,
            'status' => $request->status,
        ]);

        return redirect()->route('admin.finances.index')
            ->with('success', 'Giao dịch đã được cập nhật.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Finance $finance)
    {
        $finance->delete();

        return redirect()->route('admin.finances.index')
            ->with('success', 'Giao dịch đã được xóa.');
    }

    /**
     * Generate financial report
     */
    public function report(Request $request)
    {
        $fromDate = $request->get('from_date', Carbon::now()->startOfMonth());
        $toDate = $request->get('to_date', Carbon::now()->endOfMonth());

        $incomes = Finance::income()
            ->completed()
            ->whereBetween('transaction_date', [$fromDate, $toDate])
            ->selectRaw('category, SUM(amount) as total')
            ->groupBy('category')
            ->get();

        $expenses = Finance::expense()
            ->completed()
            ->whereBetween('transaction_date', [$fromDate, $toDate])
            ->selectRaw('category, SUM(amount) as total')
            ->groupBy('category')
            ->get();

        $totalIncome = $incomes->sum('total');
        $totalExpense = $expenses->sum('total');
        $balance = $totalIncome - $totalExpense;

        return view('admin.finances.report', compact(
            'incomes', 'expenses', 'totalIncome', 'totalExpense', 'balance', 'fromDate', 'toDate'
        ));
    }
}
