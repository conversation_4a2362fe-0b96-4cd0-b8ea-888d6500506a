<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Student;
use App\Models\ClassRoom;
use App\Models\Schedule;
use App\Models\Attendance;
use App\Models\User;
use Carbon\Carbon;

class DashboardController extends Controller
{
    public function index()
    {
        $user = auth()->user();
        $today = Carbon::today();

        $data = [
            'user' => $user,
            'today' => $today,
        ];

        // Dữ liệu chung cho tất cả roles
        $data['todaySchedules'] = Schedule::with(['class', 'teacher', 'timeSlot'])
            ->where('date', $today)
            ->where('status', 'scheduled')
            ->get();

        if ($user->isAdmin() || $user->isManager()) {
            // Dữ liệu cho Admin và Manager
            $data['totalStudents'] = Student::where('status', 'active')->count();
            $data['totalClasses'] = ClassRoom::where('status', 'active')->count();
            $data['totalTeachers'] = User::where('role', 'teacher')->where('status', 'active')->count();

            // Thống kê học sinh vắng trong tuần
            $weekStart = $today->copy()->startOfWeek();
            $weekEnd = $today->copy()->endOfWeek();

            $data['weeklyAbsent'] = Attendance::whereBetween('created_at', [$weekStart, $weekEnd])
                ->where('status', 'absent')
                ->count();

            // Thống kê học sinh vắng trong tháng
            $monthStart = $today->copy()->startOfMonth();
            $monthEnd = $today->copy()->endOfMonth();

            $data['monthlyAbsent'] = Attendance::whereBetween('created_at', [$monthStart, $monthEnd])
                ->where('status', 'absent')
                ->count();
        }

        if ($user->isTeacher()) {
            // Dữ liệu cho Teacher
            $data['mySchedules'] = Schedule::with(['class', 'timeSlot'])
                ->where('teacher_id', $user->id)
                ->where('date', $today)
                ->where('status', 'scheduled')
                ->get();

            $data['myClasses'] = ClassRoom::where('teacher_id', $user->id)
                ->where('status', 'active')
                ->withCount('activeStudents')
                ->get();
        }

        return view('dashboard', $data);
    }
}
