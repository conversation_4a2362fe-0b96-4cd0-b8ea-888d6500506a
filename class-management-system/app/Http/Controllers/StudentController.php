<?php

namespace App\Http\Controllers;

use App\Models\Student;
use App\Models\ClassRoom;
use Illuminate\Http\Request;

class StudentController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $students = Student::with('currentClass')
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return view('students.index', compact('students'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $classes = ClassRoom::where('status', 'active')->get();
        return view('students.create', compact('classes'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'birth_date' => 'required|date',
            'parent_name' => 'required|string|max:255',
            'parent_phone' => 'required|string|max:20',
            'google_drive_link' => 'nullable|url',
            'class_id' => 'nullable|exists:classes,id',
        ]);

        $student = Student::create([
            'student_code' => Student::generateStudentCode(),
            'name' => $request->name,
            'birth_date' => $request->birth_date,
            'parent_name' => $request->parent_name,
            'parent_phone' => $request->parent_phone,
            'google_drive_link' => $request->google_drive_link,
            'status' => 'active',
        ]);

        // Thêm học sinh vào lớp nếu có chọn
        if ($request->class_id) {
            $class = ClassRoom::find($request->class_id);
            if ($class && $class->canAddMoreStudents()) {
                $student->classes()->attach($request->class_id, [
                    'enrolled_at' => now()->toDateString(),
                    'status' => 'active',
                ]);
            }
        }

        return redirect()->route('students.index')
            ->with('success', 'Học sinh đã được thêm thành công.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Student $student)
    {
        $student->load(['classes', 'attendance.schedule']);
        return view('students.show', compact('student'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Student $student)
    {
        $classes = ClassRoom::where('status', 'active')->get();
        $currentClass = $student->currentClass();
        return view('students.edit', compact('student', 'classes', 'currentClass'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Student $student)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'birth_date' => 'required|date',
            'parent_name' => 'required|string|max:255',
            'parent_phone' => 'required|string|max:20',
            'google_drive_link' => 'nullable|url',
            'status' => 'required|in:active,inactive',
            'class_id' => 'nullable|exists:classes,id',
        ]);

        $student->update([
            'name' => $request->name,
            'birth_date' => $request->birth_date,
            'parent_name' => $request->parent_name,
            'parent_phone' => $request->parent_phone,
            'google_drive_link' => $request->google_drive_link,
            'status' => $request->status,
        ]);

        // Cập nhật lớp học
        $currentClass = $student->currentClass();
        if ($request->class_id != $currentClass?->id) {
            // Rời lớp cũ
            if ($currentClass) {
                $student->classes()->updateExistingPivot($currentClass->id, [
                    'status' => 'inactive',
                    'left_at' => now()->toDateString(),
                ]);
            }

            // Vào lớp mới
            if ($request->class_id) {
                $newClass = ClassRoom::find($request->class_id);
                if ($newClass && $newClass->canAddMoreStudents()) {
                    $student->classes()->attach($request->class_id, [
                        'enrolled_at' => now()->toDateString(),
                        'status' => 'active',
                    ]);
                }
            }
        }

        return redirect()->route('students.index')
            ->with('success', 'Thông tin học sinh đã được cập nhật.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Student $student)
    {
        $student->delete();

        return redirect()->route('students.index')
            ->with('success', 'Học sinh đã được xóa.');
    }
}
